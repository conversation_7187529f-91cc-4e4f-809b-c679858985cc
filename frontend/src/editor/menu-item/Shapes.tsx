"use client";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import {
  Box,
  Grid,
  Typography,
  Paper,
  useTheme,
  Tooltip,
  Chip,
  IconButton,
  Stack,
} from "@mui/material";
import { observer } from "mobx-react";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { StoreContext } from "../../store";
import { ShapeType } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";

// 定义形状分类键值
const SHAPE_CATEGORY_KEYS = {
  all: "shape_category_all",
  basic: "shape_category_basic",
  polygons: "shape_category_polygons",
  arrows: "shape_category_arrows",
  stars: "shape_category_stars",
  symbols: "shape_category_symbols",
  special: "shape_category_special",
} as const;

// 定义形状资源
const SHAPE_RESOURCES: {
  nameKey: string;
  type: ShapeType;
  category: keyof typeof SHAPE_CATEGORY_KEYS;
  description?: string;
  icon: React.ReactNode;
}[] = [
  {
    nameKey: "shape_rect",
    type: "rect",
    category: "basic",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 30,
          bgcolor: "#F44336", // 红色
          borderRadius: 1,
        }}
      />
    ),
  },
  {
    nameKey: "shape_rounded_rect",
    type: "roundedRect",
    category: "basic",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "#2196F3", // 蓝色
        }}
      />
    ),
  },
  {
    nameKey: "shape_circle",
    type: "circle",
    category: "basic",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "#FFC107", // 黄色
          borderRadius: "50%",
        }}
      />
    ),
  },
  {
    nameKey: "shape_ellipse",
    type: "ellipse",
    category: "basic",
    icon: (
      <Box
        sx={{
          width: 45,
          height: 30,
          bgcolor: "#4CAF50", // 绿色
          borderRadius: "50%",
        }}
      />
    ),
  },
  {
    nameKey: "shape_triangle",
    type: "triangle",
    category: "basic",
    icon: (
      <Box
        sx={{
          width: 0,
          height: 0,
          borderLeft: "18px solid transparent",
          borderRight: "18px solid transparent",
          borderBottom: "32px solid #9C27B0", // 紫色
        }}
      />
    ),
  },
  {
    nameKey: "shape_line",
    type: "line",
    category: "basic",
    icon: (
      <Box
        sx={{
          width: 50,
          height: 4,
          bgcolor: "#FF9800", // 橙色
        }}
      />
    ),
  },
  {
    nameKey: "shape_diamond",
    type: "diamond",
    category: "polygons",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 28,
            height: 28,
            bgcolor: "#2196F3", // 蓝色
            transform: "rotate(45deg)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_pentagon",
    type: "pentagon",
    category: "polygons",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#F44336", // 红色
            clipPath: "polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_hexagon",
    type: "hexagon",
    category: "polygons",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#2196F3", // 蓝色
            clipPath:
              "polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_octagon",
    type: "octagon",
    category: "polygons",
    description: "绘制一个八边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#FFC107", // 黄色
            clipPath:
              "polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_right_arrow",
    type: "rightArrow",
    category: "arrows",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 0,
            height: 0,
            borderTop: "8px solid transparent",
            borderBottom: "8px solid transparent",
            borderLeft: "20px solid #2196F3", // 蓝色
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              left: "-30px",
              top: "-4px",
              width: "20px",
              height: "8px",
              bgcolor: "#2196F3",
            },
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_up_arrow",
    type: "upArrow",
    category: "arrows",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 0,
            height: 0,
            borderLeft: "8px solid transparent",
            borderRight: "8px solid transparent",
            borderBottom: "20px solid #F44336", // 红色
            position: "relative",
            "&::after": {
              content: '""',
              position: "absolute",
              top: "20px",
              left: "-4px",
              width: "8px",
              height: "15px",
              bgcolor: "#F44336",
            },
          }}
        />
      </Box>
    ),
  },

  {
    nameKey: "shape_cross",
    type: "cross",
    category: "symbols",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: "25px",
            height: "25px",
            position: "relative",
            "&::before, &::after": {
              content: '""',
              position: "absolute",
              left: "11px",
              top: "2px",
              width: "3px",
              height: "20px",
              bgcolor: "#FFC107", // 黄色
              borderRadius: "2px",
            },
            "&::before": {
              transform: "rotate(45deg)",
            },
            "&::after": {
              transform: "rotate(-45deg)",
            },
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_down_arrow",
    type: "downArrow",
    category: "arrows",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 0,
            height: 0,
            borderLeft: "8px solid transparent",
            borderRight: "8px solid transparent",
            borderTop: "20px solid #9C27B0", // 紫色
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              top: "-35px",
              left: "-4px",
              width: "8px",
              height: "15px",
              bgcolor: "#9C27B0",
            },
          }}
        />
      </Box>
    ),
  },

  {
    nameKey: "shape_star",
    type: "star",
    category: "stars",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#FFC107", // 黄色
            clipPath:
              "polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_four_point_star",
    type: "fourPointStar",
    category: "stars",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 30,
            height: 30,
            bgcolor: "#2196F3", // 蓝色
            clipPath:
              "polygon(50% 0%, 70% 30%, 100% 20%, 80% 50%, 100% 80%, 70% 70%, 50% 100%, 30% 70%, 0% 80%, 20% 50%, 0% 20%, 30% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_six_point_star",
    type: "sixPointStar",
    category: "stars",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 32,
            height: 32,
            bgcolor: "#F44336", // 红色
            clipPath:
              "polygon(50% 0%, 65% 30%, 100% 25%, 75% 50%, 100% 75%, 65% 70%, 50% 100%, 35% 70%, 0% 75%, 25% 50%, 0% 25%, 35% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_eight_point_star",
    type: "eightPointStar",
    category: "stars",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 30,
            height: 30,
            bgcolor: "#FF9800", // 橙色
            clipPath:
              "polygon(50% 0%, 65% 25%, 90% 15%, 80% 40%, 100% 50%, 80% 60%, 90% 85%, 65% 75%, 50% 100%, 35% 75%, 10% 85%, 20% 60%, 0% 50%, 20% 40%, 10% 15%, 35% 25%)",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_sun_burst",
    type: "sunBurst",
    category: "stars",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 28,
            height: 28,
            bgcolor: "#FFC107", // 黄色
            borderRadius: "50%",
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              top: "-6px",
              left: "-6px",
              width: "40px",
              height: "40px",
              bgcolor: "#FFC107",
              clipPath:
                "polygon(50% 0%, 55% 25%, 75% 15%, 65% 35%, 85% 35%, 65% 50%, 85% 65%, 65% 65%, 75% 85%, 55% 75%, 50% 100%, 45% 75%, 25% 85%, 35% 65%, 15% 65%, 35% 50%, 15% 35%, 35% 35%, 25% 15%, 45% 25%)",
              borderRadius: 0,
            },
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_semicircle",
    type: "semicircle",
    category: "special",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 35,
            height: 18,
            bgcolor: "#9C27B0", // 紫色
            borderTopLeftRadius: "35px",
            borderTopRightRadius: "35px",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_quarter_circle",
    type: "quarterCircle",
    category: "special",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "flex-end",
          justifyContent: "flex-start",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 25,
            height: 25,
            bgcolor: "#2196F3", // 蓝色
            borderBottomLeftRadius: "25px",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_ring",
    type: "ring",
    category: "special",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 30,
            height: 30,
            bgcolor: "#4CAF50", // 绿色
            borderRadius: "50%",
            position: "relative",
            "&::after": {
              content: '""',
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              width: "15px",
              height: "15px",
              bgcolor: "#f5f5f5",
              borderRadius: "50%",
            },
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_half_ring",
    type: "halfRing",
    category: "special",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 28,
            height: 14,
            border: "4px solid #FF9800", // 橙色
            borderBottom: "none",
            borderTopLeftRadius: "28px",
            borderTopRightRadius: "28px",
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_plus",
    type: "plus",
    category: "symbols",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: "20px",
            height: "20px",
            position: "relative",
            "&::before, &::after": {
              content: '""',
              position: "absolute",
              bgcolor: "#2196F3", // 蓝色
            },
            "&::before": {
              left: "50%",
              top: 0,
              transform: "translateX(-50%)",
              width: "4px",
              height: "20px",
            },
            "&::after": {
              top: "50%",
              left: 0,
              transform: "translateY(-50%)",
              width: "20px",
              height: "4px",
            },
          }}
        />
      </Box>
    ),
  },
  {
    nameKey: "shape_arch",
    type: "arch",
    category: "special",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "#673AB7", // 深紫色，替换原来的grey.400
            borderTopLeftRadius: "100%",
            borderTopRightRadius: "100%",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              bottom: 0,
              left: 0,
              width: "100%",
              height: "50%",
              bgcolor: "#673AB7", // 深紫色，替换原来的grey.400
            }}
          />
        </Box>
      </Box>
    ),
  },
  {
    nameKey: "shape_parallelogram",
    type: "parallelogram",
    category: "polygons",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 30,
            bgcolor: "#009688", // 蓝绿色，替换原来的grey.400
            clipPath: "polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)",
          }}
        />
      </Box>
    ),
  },
];

// 形状项组件
interface ShapeItemProps {
  shape: {
    nameKey: string;
    type: ShapeType;
    category: keyof typeof SHAPE_CATEGORY_KEYS;
    description?: string;
    icon: React.ReactNode;
  };
  onClick: () => void;
  selected: boolean;
  t: (key: string) => string;
}

const ShapeItem: React.FC<ShapeItemProps> = ({
  shape,
  onClick,
  selected,
  t,
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Paper
      draggable
      onDragStart={(e) => {
        const dragData = {
          type: "shape",
          shapeType: shape.type,
          metadata: {
            nameKey: shape.nameKey,
            category: shape.category,
          },
        };
        e.dataTransfer.setData("application/json", JSON.stringify(dragData));
        e.dataTransfer.effectAllowed = "copy";
      }}
      elevation={0}
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 1.5,
        cursor: "grab",
        transition: "all 0.2s",
        bgcolor: "background.paper",
        borderRadius: 2,
        aspectRatio: "1/1",
        "&:active": {
          cursor: "grabbing",
        },
        "&:hover": {
          bgcolor: theme.palette.background.default,
          transform: "scale(1.05)",
          boxShadow: 2,
        },
        border: `2px solid ${theme.palette.divider}`,
      }}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick();
          e.preventDefault();
        }
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          height: "100%",
        }}
      >
        {shape.icon}
      </Box>
    </Paper>
  );
};

export const Shapes = observer(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [selectedCategory, setSelectedCategory] =
    useState<keyof typeof SHAPE_CATEGORY_KEYS>("all");
  const [selectedShape, setSelectedShape] = useState<ShapeType | null>(null);

  // 分类滚动相关状态
  const categoriesContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // 检查分类滚动状态
  const checkScroll = useCallback(() => {
    const container = categoriesContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  }, []);

  // 监听分类容器滚动和窗口大小变化
  useEffect(() => {
    const container = categoriesContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener("scroll", checkScroll);
      window.addEventListener("resize", checkScroll);
      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [checkScroll]);

  // 左右滚动分类
  const handleScrollLeft = () => {
    if (categoriesContainerRef.current) {
      categoriesContainerRef.current.scrollBy({
        left: -200,
        behavior: "smooth",
      });
    }
  };

  const handleScrollRight = () => {
    if (categoriesContainerRef.current) {
      categoriesContainerRef.current.scrollBy({
        left: 200,
        behavior: "smooth",
      });
    }
  };

  const handleCategoryChange = (category: keyof typeof SHAPE_CATEGORY_KEYS) => {
    setSelectedCategory(category);
  };

  const handleAddShape = (shapeType: ShapeType) => {
    store.addShapeElement(shapeType);
    setSelectedShape(shapeType);

    // 添加反馈效果
    setTimeout(() => {
      setSelectedShape(null);
    }, 500);
  };

  return (
    <Box
      sx={{
        width: 250,
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">{t("shapes")}</Typography>
      </Box>

      <Box
        sx={{
          bgcolor: "grey.100",
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        {/* 分类选择区域 - 水平滚动设计 */}
        <Box
          sx={{
            position: "relative",
            "&:hover .scroll-button": {
              opacity: 1,
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              overflowX: "auto",
              scrollBehavior: "smooth",
              "&::-webkit-scrollbar": { display: "none" },
              msOverflowStyle: "none",
              scrollbarWidth: "none",
              px: 1,
              borderRadius: 2,
            }}
            ref={categoriesContainerRef}
          >
            <Stack direction="row" spacing={1}>
              {Object.entries(SHAPE_CATEGORY_KEYS).map(
                ([key, translationKey]) => (
                  <Chip
                    key={key}
                    label={t(translationKey)}
                    clickable
                    color={selectedCategory === key ? "primary" : "default"}
                    onClick={() =>
                      handleCategoryChange(
                        key as keyof typeof SHAPE_CATEGORY_KEYS
                      )
                    }
                    sx={{ whiteSpace: "nowrap" }}
                  />
                )
              )}
            </Stack>
          </Box>

          {/* 左右滚动按钮 */}
          {canScrollLeft && (
            <IconButton
              className="scroll-button"
              size="small"
              onClick={handleScrollLeft}
              sx={{
                position: "absolute",
                left: 0,
                top: "50%",
                transform: "translateY(-50%)",
                bgcolor: "background.paper",
                boxShadow: 1,
                opacity: 0.7,
                "&:hover": { opacity: 1 },
                zIndex: 2,
              }}
            >
              <ChevronLeftIcon fontSize="small" />
            </IconButton>
          )}

          {canScrollRight && (
            <IconButton
              className="scroll-button"
              size="small"
              onClick={handleScrollRight}
              sx={{
                position: "absolute",
                right: 0,
                top: "50%",
                transform: "translateY(-50%)",
                bgcolor: "background.paper",
                boxShadow: 1,
                opacity: 0.7,
                "&:hover": { opacity: 1 },
                zIndex: 2,
              }}
            >
              <ChevronRightIcon fontSize="small" />
            </IconButton>
          )}
        </Box>
      </Box>

      <Box
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          p: 1.5,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
        }}
      >
        <Grid container spacing={1.5}>
          {SHAPE_RESOURCES.filter(
            (shape) =>
              selectedCategory === "all" || shape.category === selectedCategory
          ).map((shape, index) => (
            <Grid item xs={4} key={shape.type}>
              <ShapeItem
                shape={shape}
                selected={selectedShape === shape.type}
                onClick={() => handleAddShape(shape.type)}
                t={t}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
});
